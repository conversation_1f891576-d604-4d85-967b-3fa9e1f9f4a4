/**
 * 资源跟踪器
 * 用于跟踪Three.js资源的内存占用
 */
import * as THREE from 'three';
import { MemoryAnalyzer, ResourceType } from './MemoryAnalyzer';
import { Debug } from './Debug';

/**
 * 资源跟踪器配置接口
 */
export interface ResourceTrackerConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否启用调试输出 */
  debug?: boolean;
  /** 是否自动跟踪所有资源 */
  autoTrackAll?: boolean;
  /** 是否自动计算资源大小 */
  autoCalculateSize?: boolean;
  /** 是否启用自动清理 */
  autoCleanup?: boolean;
  /** 自动清理间隔（毫秒） */
  autoCleanupInterval?: number;
}

/**
 * 资源跟踪器类
 * 用于跟踪Three.js资源的内存占用
 */
export class ResourceTracker {
  private static instance: ResourceTracker;
  
  /** 配置 */
  private config: ResourceTrackerConfig = {
    enabled: false,
    debug: false,
    autoTrackAll: true,
    autoCalculateSize: true,
    autoCleanup: false,
    autoCleanupInterval: 60000
  };
  
  /** 内存分析器 */
  private memoryAnalyzer: MemoryAnalyzer;
  
  /** 已跟踪的资源映射 */
  private trackedResources: Map<string, THREE.Object3D | THREE.Material | THREE.Texture | THREE.BufferGeometry> = new Map();
  
  /** 资源ID计数器 */
  private idCounter: number = 0;

  /** 原始Three.js方法的备份 */
  private originalMethods: {
    textureDispose?: () => void;
    geometryDispose?: () => void;
    materialDispose?: () => void;
    object3DRemoveFromParent?: () => THREE.Object3D;
  } = {};

  /** 是否已经覆盖了Three.js方法 */
  private methodsOverridden: boolean = false;

  /** 自动清理定时器 */
  private autoCleanupTimer?: NodeJS.Timeout;

  /** 自动清理间隔（毫秒） */
  private autoCleanupInterval: number = 60000; // 1分钟
  
  /**
   * 获取单例实例
   */
  public static getInstance(): ResourceTracker {
    if (!ResourceTracker.instance) {
      ResourceTracker.instance = new ResourceTracker();
    }
    return ResourceTracker.instance;
  }
  
  /**
   * 私有构造函数
   */
  private constructor() {
    this.memoryAnalyzer = MemoryAnalyzer.getInstance();
  }
  
  /**
   * 配置资源跟踪器
   * @param config 配置
   */
  public configure(config: ResourceTrackerConfig): void {
    this.config = {
      ...this.config,
      ...config
    };

    // 更新自动清理间隔
    if (config.autoCleanupInterval !== undefined) {
      this.autoCleanupInterval = config.autoCleanupInterval;
    }

    if (this.config.debug) {
      Debug.log('资源跟踪器', '配置已更新', this.config);
    }
  }
  
  /**
   * 启动资源跟踪器
   */
  public start(): void {
    if (!this.config.enabled) {
      return;
    }
    
    // 确保内存分析器已启动
    this.memoryAnalyzer.configure({
      enabled: true,
      enableLeakDetection: true
    });
    this.memoryAnalyzer.start();
    
    if (this.config.debug) {
      Debug.log('资源跟踪器', '已启动');
    }
    
    // 如果配置为自动跟踪所有资源，则覆盖Three.js的创建方法
    if (this.config.autoTrackAll) {
      this.overrideThreeMethods();
    }

    // 启动自动清理
    if (this.config.autoCleanup) {
      this.startAutoCleanup();
    }
  }
  
  /**
   * 停止资源跟踪器
   */
  public stop(): void {
    // 停止自动清理
    this.stopAutoCleanup();

    // 恢复原始的Three.js方法
    this.restoreThreeMethods();

    // 清理所有跟踪的资源
    this.trackedResources.clear();

    // 停止内存分析器
    this.memoryAnalyzer.stop();

    if (this.config.debug) {
      Debug.log('资源跟踪器', '已停止');
    }
  }
  
  /**
   * 跟踪资源
   * @param resource Three.js资源
   * @param name 资源名称（可选）
   * @returns 资源ID
   */
  public track(resource: THREE.Object3D | THREE.Material | THREE.Texture | THREE.BufferGeometry, name?: string): string {
    if (!this.config.enabled) {
      return '';
    }
    
    // 生成资源ID
    const resourceId = this.generateResourceId(resource);
    
    // 如果已经跟踪了该资源，则更新访问时间
    if (this.trackedResources.has(resourceId)) {
      const existingResource = this.memoryAnalyzer.getResource(resourceId);
      if (existingResource) {
        this.memoryAnalyzer.updateResource(resourceId, {
          refCount: existingResource.refCount + 1
        });
      }
      return resourceId;
    }
    
    // 添加到跟踪映射
    this.trackedResources.set(resourceId, resource);
    
    // 确定资源类型
    const resourceType = this.getResourceType(resource);
    
    // 计算资源大小
    const resourceSize = this.calculateResourceSize(resource);
    
    // 确定资源名称
    const resourceName = name || this.getResourceName(resource);
    
    // 注册到内存分析器
    this.memoryAnalyzer.registerResource({
      id: resourceId,
      name: resourceName,
      type: resourceType,
      size: resourceSize,
      refCount: 1
    });
    
    if (this.config.debug) {
      Debug.log('资源跟踪器', `已跟踪资源: ${resourceId} (${resourceType}, ${this.formatBytes(resourceSize)})`);
    }
    
    return resourceId;
  }
  
  /**
   * 取消跟踪资源
   * @param resourceId 资源ID
   */
  public untrack(resourceId: string): void {
    if (!this.config.enabled || !this.trackedResources.has(resourceId)) {
      return;
    }
    
    // 从跟踪映射中移除
    this.trackedResources.delete(resourceId);
    
    // 标记为已释放
    this.memoryAnalyzer.disposeResource(resourceId);
    
    if (this.config.debug) {
      Debug.log('资源跟踪器', `已取消跟踪资源: ${resourceId}`);
    }
  }
  
  /**
   * 取消跟踪资源
   * @param resource Three.js资源
   */
  public untrackResource(resource: THREE.Object3D | THREE.Material | THREE.Texture | THREE.BufferGeometry): void {
    const resourceId = this.getResourceId(resource);
    if (resourceId) {
      this.untrack(resourceId);
    }
  }
  
  /**
   * 获取资源ID
   * @param resource Three.js资源
   * @returns 资源ID，如果未跟踪则返回空字符串
   */
  public getResourceId(resource: THREE.Object3D | THREE.Material | THREE.Texture | THREE.BufferGeometry): string {
    for (const [id, trackedResource] of this.trackedResources.entries()) {
      if (trackedResource === resource) {
        return id;
      }
    }
    return '';
  }
  
  /**
   * 生成资源ID
   * @param resource Three.js资源
   * @returns 资源ID
   */
  private generateResourceId(resource: THREE.Object3D | THREE.Material | THREE.Texture | THREE.BufferGeometry): string {
    // 尝试使用资源的UUID
    if ('uuid' in resource && typeof resource.uuid === 'string') {
      return resource.uuid;
    }
    
    // 如果没有UUID，则使用递增计数器
    return `resource-${++this.idCounter}`;
  }
  
  /**
   * 获取资源类型
   * @param resource Three.js资源
   * @returns 资源类型
   */
  private getResourceType(resource: THREE.Object3D | THREE.Material | THREE.Texture | THREE.BufferGeometry): ResourceType {
    if (resource instanceof THREE.Texture) {
      return ResourceType.TEXTURE;
    } else if (resource instanceof THREE.BufferGeometry) {
      return ResourceType.GEOMETRY;
    } else if (resource instanceof THREE.Material) {
      return ResourceType.MATERIAL;
    } else if (resource instanceof THREE.Object3D) {
      if (resource instanceof THREE.Mesh || resource instanceof THREE.SkinnedMesh) {
        return ResourceType.MODEL;
      }
      return ResourceType.OTHER;
    }
    return ResourceType.OTHER;
  }
  
  /**
   * 获取资源名称
   * @param resource Three.js资源
   * @returns 资源名称
   */
  private getResourceName(resource: THREE.Object3D | THREE.Material | THREE.Texture | THREE.BufferGeometry): string {
    if ('name' in resource && typeof resource.name === 'string' && resource.name) {
      return resource.name;
    }
    
    const type = this.getResourceType(resource);
    return `${type}-${this.idCounter}`;
  }
  
  /**
   * 计算资源大小
   * @param resource Three.js资源
   * @returns 资源大小（字节）
   */
  private calculateResourceSize(resource: THREE.Object3D | THREE.Material | THREE.Texture | THREE.BufferGeometry): number {
    if (!this.config.autoCalculateSize) {
      return 0;
    }
    
    try {
      if (resource instanceof THREE.Texture) {
        return this.calculateTextureSize(resource);
      } else if (resource instanceof THREE.BufferGeometry) {
        return this.calculateGeometrySize(resource);
      } else if (resource instanceof THREE.Material) {
        return this.calculateMaterialSize(resource);
      } else if (resource instanceof THREE.Object3D) {
        return this.calculateObject3DSize(resource);
      }
    } catch (error) {
      if (this.config.debug) {
        Debug.warn('资源跟踪器', `计算资源大小时出错: ${error}`);
      }
    }
    
    return 0;
  }
  
  /**
   * 计算纹理大小
   * @param texture 纹理
   * @returns 纹理大小（字节）
   */
  private calculateTextureSize(texture: THREE.Texture): number {
    if (!texture.image) {
      return 0;
    }
    
    let width = 0;
    let height = 0;
    
    if (texture.image instanceof HTMLImageElement || texture.image instanceof HTMLCanvasElement || texture.image instanceof HTMLVideoElement) {
      width = texture.image.width;
      height = texture.image.height;
    } else if (texture.image instanceof ImageData) {
      width = texture.image.width;
      height = texture.image.height;
    } else if (Array.isArray(texture.image)) {
      // 立方体贴图
      const firstImage = texture.image[0];
      if (firstImage) {
        width = firstImage.width || 0;
        height = firstImage.height || 0;
        return width * height * 4 * 6; // 6个面
      }
      return 0;
    } else {
      return 0;
    }
    
    // 估算纹理内存（RGBA，每像素4字节）
    return width * height * 4;
  }
  
  /**
   * 计算几何体大小
   * @param geometry 几何体
   * @returns 几何体大小（字节）
   */
  private calculateGeometrySize(geometry: THREE.BufferGeometry): number {
    let totalSize = 0;
    
    // 计算所有属性的大小
    for (const name in geometry.attributes) {
      const attribute = geometry.attributes[name];
      if (attribute && attribute.array) {
        // 检查是否是 TypedArray
        if ('byteLength' in attribute.array) {
          totalSize += (attribute.array as any).byteLength;
        } else {
          // 如果不是 TypedArray，估算大小
          totalSize += attribute.array.length * 4; // 假设每个元素4字节
        }
      }
    }

    // 计算索引的大小
    if (geometry.index && geometry.index.array) {
      // 检查是否是 TypedArray
      if ('byteLength' in geometry.index.array) {
        totalSize += (geometry.index.array as any).byteLength;
      } else {
        // 如果不是 TypedArray，估算大小
        totalSize += geometry.index.array.length * 4; // 假设每个元素4字节
      }
    }
    
    return totalSize;
  }
  
  /**
   * 计算材质大小
   * @param material 材质
   * @returns 材质大小（字节）
   */
  private calculateMaterialSize(material: THREE.Material): number {
    // 材质本身的大小很难精确计算，这里使用估算值
    let size = 1024; // 基础大小
    
    // 如果是标准材质，检查其纹理
    if (material instanceof THREE.MeshStandardMaterial) {
      if (material.map) size += this.calculateTextureSize(material.map);
      if (material.normalMap) size += this.calculateTextureSize(material.normalMap);
      if (material.roughnessMap) size += this.calculateTextureSize(material.roughnessMap);
      if (material.metalnessMap) size += this.calculateTextureSize(material.metalnessMap);
      if (material.aoMap) size += this.calculateTextureSize(material.aoMap);
      if (material.emissiveMap) size += this.calculateTextureSize(material.emissiveMap);
      if (material.displacementMap) size += this.calculateTextureSize(material.displacementMap);
    } else if (material instanceof THREE.MeshBasicMaterial || material instanceof THREE.MeshLambertMaterial || material instanceof THREE.MeshPhongMaterial) {
      if ('map' in material && material.map) size += this.calculateTextureSize(material.map);
    }
    
    return size;
  }
  
  /**
   * 计算3D对象大小
   * @param object 3D对象
   * @returns 3D对象大小（字节）
   */
  private calculateObject3DSize(object: THREE.Object3D): number {
    let size = 1024; // 基础大小
    
    // 如果是网格，计算几何体和材质的大小
    if (object instanceof THREE.Mesh || object instanceof THREE.SkinnedMesh) {
      if (object.geometry) {
        size += this.calculateGeometrySize(object.geometry);
      }
      
      if (object.material) {
        if (Array.isArray(object.material)) {
          for (const material of object.material) {
            size += this.calculateMaterialSize(material);
          }
        } else {
          size += this.calculateMaterialSize(object.material);
        }
      }
    }
    
    return size;
  }
  
  /**
   * 覆盖Three.js方法以自动跟踪资源
   */
  private overrideThreeMethods(): void {
    if (this.methodsOverridden) {
      return;
    }

    try {
      // 覆盖Texture的dispose方法
      this.originalMethods.textureDispose = THREE.Texture.prototype.dispose;
      THREE.Texture.prototype.dispose = function(this: THREE.Texture) {
        const tracker = ResourceTracker.getInstance();
        tracker.untrackResource(this);

        // 调用原始的dispose方法
        if (tracker.originalMethods.textureDispose) {
          tracker.originalMethods.textureDispose.call(this);
        }
      };

      // 覆盖BufferGeometry的dispose方法
      this.originalMethods.geometryDispose = THREE.BufferGeometry.prototype.dispose;
      THREE.BufferGeometry.prototype.dispose = function(this: THREE.BufferGeometry) {
        const tracker = ResourceTracker.getInstance();
        tracker.untrackResource(this);

        // 调用原始的dispose方法
        if (tracker.originalMethods.geometryDispose) {
          tracker.originalMethods.geometryDispose.call(this);
        }
      };

      // 覆盖Material的dispose方法
      this.originalMethods.materialDispose = THREE.Material.prototype.dispose;
      THREE.Material.prototype.dispose = function(this: THREE.Material) {
        const tracker = ResourceTracker.getInstance();
        tracker.untrackResource(this);

        // 调用原始的dispose方法
        if (tracker.originalMethods.materialDispose) {
          tracker.originalMethods.materialDispose.call(this);
        }
      };

      // 覆盖Object3D的removeFromParent方法（用于检测对象被移除）
      this.originalMethods.object3DRemoveFromParent = THREE.Object3D.prototype.removeFromParent;
      const originalRemoveFromParent = THREE.Object3D.prototype.removeFromParent;
      THREE.Object3D.prototype.removeFromParent = function(this: THREE.Object3D) {
        const tracker = ResourceTracker.getInstance();

        // 递归取消跟踪所有子对象
        this.traverse((child) => {
          tracker.untrackResource(child);
        });

        // 调用原始方法
        return originalRemoveFromParent.call(this);
      };

      this.methodsOverridden = true;

      if (this.config.debug) {
        Debug.log('资源跟踪器', 'Three.js方法已覆盖，启用自动跟踪');
      }
    } catch (error) {
      if (this.config.debug) {
        Debug.error('资源跟踪器', '覆盖Three.js方法时出错:', error);
      }
    }
  }

  /**
   * 恢复原始的Three.js方法
   */
  private restoreThreeMethods(): void {
    if (!this.methodsOverridden) {
      return;
    }

    try {
      // 恢复Texture的dispose方法
      if (this.originalMethods.textureDispose) {
        THREE.Texture.prototype.dispose = this.originalMethods.textureDispose;
      }

      // 恢复BufferGeometry的dispose方法
      if (this.originalMethods.geometryDispose) {
        THREE.BufferGeometry.prototype.dispose = this.originalMethods.geometryDispose;
      }

      // 恢复Material的dispose方法
      if (this.originalMethods.materialDispose) {
        THREE.Material.prototype.dispose = this.originalMethods.materialDispose;
      }

      // 恢复Object3D的removeFromParent方法
      if (this.originalMethods.object3DRemoveFromParent) {
        THREE.Object3D.prototype.removeFromParent = this.originalMethods.object3DRemoveFromParent;
      }

      this.methodsOverridden = false;

      if (this.config.debug) {
        Debug.log('资源跟踪器', 'Three.js方法已恢复');
      }
    } catch (error) {
      if (this.config.debug) {
        Debug.error('资源跟踪器', '恢复Three.js方法时出错:', error);
      }
    }
  }

  /**
   * 启动自动清理
   */
  private startAutoCleanup(): void {
    if (this.autoCleanupTimer) {
      return; // 已经启动
    }

    this.autoCleanupTimer = setInterval(() => {
      this.performAutoCleanup();
    }, this.autoCleanupInterval);

    if (this.config.debug) {
      Debug.log('资源跟踪器', `自动清理已启动，间隔: ${this.autoCleanupInterval}ms`);
    }
  }

  /**
   * 停止自动清理
   */
  private stopAutoCleanup(): void {
    if (this.autoCleanupTimer) {
      clearInterval(this.autoCleanupTimer);
      this.autoCleanupTimer = undefined;

      if (this.config.debug) {
        Debug.log('资源跟踪器', '自动清理已停止');
      }
    }
  }

  /**
   * 执行自动清理
   */
  private performAutoCleanup(): void {
    const beforeCount = this.trackedResources.size;
    const resourcesToRemove: string[] = [];

    // 检查每个跟踪的资源是否仍然有效
    for (const [id, resource] of this.trackedResources.entries()) {
      if (this.isResourceDisposed(resource)) {
        resourcesToRemove.push(id);
      }
    }

    // 移除已释放的资源
    for (const id of resourcesToRemove) {
      this.untrack(id);
    }

    const afterCount = this.trackedResources.size;
    const cleanedCount = beforeCount - afterCount;

    if (this.config.debug && cleanedCount > 0) {
      Debug.log('资源跟踪器', `自动清理完成，清理了 ${cleanedCount} 个资源`);
    }
  }

  /**
   * 检查资源是否已被释放
   * @param resource Three.js资源
   * @returns 是否已释放
   */
  private isResourceDisposed(resource: THREE.Object3D | THREE.Material | THREE.Texture | THREE.BufferGeometry): boolean {
    try {
      // 检查纹理是否已释放
      if (resource instanceof THREE.Texture) {
        return !resource.image || resource.image === null;
      }

      // 检查几何体是否已释放
      if (resource instanceof THREE.BufferGeometry) {
        return Object.keys(resource.attributes).length === 0;
      }

      // 检查材质是否已释放
      if (resource instanceof THREE.Material) {
        // 材质没有明确的释放标志，这里使用一个简单的检查
        return false;
      }

      // 检查3D对象是否已从场景中移除
      if (resource instanceof THREE.Object3D) {
        return !resource.parent && resource !== resource.parent;
      }

      return false;
    } catch (error) {
      // 如果访问资源时出错，可能已经被释放
      return true;
    }
  }

  /**
   * 获取所有跟踪的资源
   * @returns 资源列表
   */
  public getTrackedResources(): Array<{
    id: string;
    resource: THREE.Object3D | THREE.Material | THREE.Texture | THREE.BufferGeometry;
    info: any;
  }> {
    const resources: Array<{
      id: string;
      resource: THREE.Object3D | THREE.Material | THREE.Texture | THREE.BufferGeometry;
      info: any;
    }> = [];

    for (const [id, resource] of this.trackedResources.entries()) {
      const info = this.memoryAnalyzer.getResource(id);
      resources.push({ id, resource, info });
    }

    return resources;
  }

  /**
   * 获取资源统计信息
   * @returns 统计信息
   */
  public getResourceStats(): {
    totalResources: number;
    totalMemory: number;
    byType: Record<string, { count: number; memory: number }>;
  } {
    const stats = {
      totalResources: 0,
      totalMemory: 0,
      byType: {} as Record<string, { count: number; memory: number }>
    };

    for (const [id] of this.trackedResources.entries()) {
      const info = this.memoryAnalyzer.getResource(id);
      if (info) {
        stats.totalResources++;
        stats.totalMemory += info.size;

        if (!stats.byType[info.type]) {
          stats.byType[info.type] = { count: 0, memory: 0 };
        }
        stats.byType[info.type].count++;
        stats.byType[info.type].memory += info.size;
      }
    }

    return stats;
  }

  /**
   * 清理所有跟踪的资源
   */
  public clearAll(): void {
    for (const [id] of this.trackedResources.entries()) {
      this.untrack(id);
    }

    if (this.config.debug) {
      Debug.log('资源跟踪器', '已清理所有跟踪的资源');
    }
  }

  /**
   * 自动跟踪场景中的所有资源
   * @param scene Three.js场景
   */
  public trackScene(scene: THREE.Scene): void {
    if (!this.config.enabled) {
      return;
    }

    scene.traverse((object) => {
      // 跟踪对象本身
      this.track(object);

      // 如果是网格，跟踪其几何体和材质
      if (object instanceof THREE.Mesh || object instanceof THREE.SkinnedMesh) {
        if (object.geometry) {
          this.track(object.geometry);
        }

        if (object.material) {
          if (Array.isArray(object.material)) {
            object.material.forEach(material => this.track(material));
          } else {
            this.track(object.material);
          }
        }
      }
    });

    if (this.config.debug) {
      const stats = this.getResourceStats();
      Debug.log('资源跟踪器', `已跟踪场景资源: ${stats.totalResources}个资源, 总内存: ${this.formatBytes(stats.totalMemory)}`);
    }
  }

  /**
   * 取消跟踪场景中的所有资源
   * @param scene Three.js场景
   */
  public untrackScene(scene: THREE.Scene): void {
    scene.traverse((object) => {
      this.untrackResource(object);

      if (object instanceof THREE.Mesh || object instanceof THREE.SkinnedMesh) {
        if (object.geometry) {
          this.untrackResource(object.geometry);
        }

        if (object.material) {
          if (Array.isArray(object.material)) {
            object.material.forEach(material => this.untrackResource(material));
          } else {
            this.untrackResource(object.material);
          }
        }
      }
    });

    if (this.config.debug) {
      Debug.log('资源跟踪器', '已取消跟踪场景资源');
    }
  }

  /**
   * 生成资源报告
   * @returns 详细的资源报告
   */
  public generateReport(): {
    summary: {
      totalResources: number;
      totalMemory: string;
      timestamp: string;
    };
    byType: Record<string, {
      count: number;
      memory: string;
      percentage: number;
    }>;
    resources: Array<{
      id: string;
      name: string;
      type: string;
      size: string;
      refCount: number;
    }>;
  } {
    const stats = this.getResourceStats();
    const resources = this.getTrackedResources();

    const report = {
      summary: {
        totalResources: stats.totalResources,
        totalMemory: this.formatBytes(stats.totalMemory),
        timestamp: new Date().toISOString()
      },
      byType: {} as Record<string, {
        count: number;
        memory: string;
        percentage: number;
      }>,
      resources: [] as Array<{
        id: string;
        name: string;
        type: string;
        size: string;
        refCount: number;
      }>
    };

    // 按类型统计
    for (const [type, typeStats] of Object.entries(stats.byType)) {
      report.byType[type] = {
        count: typeStats.count,
        memory: this.formatBytes(typeStats.memory),
        percentage: stats.totalMemory > 0 ? (typeStats.memory / stats.totalMemory) * 100 : 0
      };
    }

    // 资源详情
    for (const { id, info } of resources) {
      if (info) {
        report.resources.push({
          id,
          name: info.name,
          type: info.type,
          size: this.formatBytes(info.size),
          refCount: info.refCount
        });
      }
    }

    return report;
  }

  /**
   * 打印资源报告到控制台
   */
  public printReport(): void {
    const report = this.generateReport();

    console.group('🔍 资源跟踪器报告');
    console.log(`📊 总资源数: ${report.summary.totalResources}`);
    console.log(`💾 总内存占用: ${report.summary.totalMemory}`);
    console.log(`⏰ 生成时间: ${report.summary.timestamp}`);

    console.group('📈 按类型统计');
    for (const [type, stats] of Object.entries(report.byType)) {
      console.log(`${type}: ${stats.count}个 (${stats.memory}, ${stats.percentage.toFixed(1)}%)`);
    }
    console.groupEnd();

    if (report.resources.length > 0) {
      console.group('📋 资源详情');
      console.table(report.resources);
      console.groupEnd();
    }

    console.groupEnd();
  }

  /**
   * 格式化字节数
   * @param bytes 字节数
   * @returns 格式化后的字符串
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
