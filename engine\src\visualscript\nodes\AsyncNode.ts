/**
 * 视觉脚本异步节点
 * 异步节点用于执行异步操作，如定时器、网络请求等
 */
import { FlowNode, FlowNodeOptions } from './FlowNode';
import { NodeCategory, NodeType, SocketType, SocketDirection } from './Node';

/**
 * 异步节点选项
 */
export interface AsyncNodeOptions extends FlowNodeOptions {
  /** 超时时间（毫秒） */
  timeout?: number;
  /** 最大重试次数 */
  maxRetries?: number;
  /** 重试延迟（毫秒） */
  retryDelay?: number;
  /** 是否支持取消 */
  cancelable?: boolean;
  /** 是否支持进度报告 */
  progressReporting?: boolean;
}

/**
 * 异步节点状态
 */
export enum AsyncNodeState {
  /** 空闲 */
  IDLE = 'idle',
  /** 运行中 */
  RUNNING = 'running',
  /** 已完成 */
  COMPLETED = 'completed',
  /** 已取消 */
  CANCELED = 'canceled',
  /** 出错 */
  ERROR = 'error'
}

/**
 * 异步节点基类
 */
export class AsyncNode extends FlowNode {
  /** 节点类型 */
  public readonly nodeType: NodeType = NodeType.ASYNC;
  
  /** 节点类别 */
  public readonly category: NodeCategory = NodeCategory.FLOW;
  
  /** 超时时间（毫秒） */
  protected timeout: number;

  /** 最大重试次数 */
  protected maxRetries: number;

  /** 重试延迟（毫秒） */
  protected retryDelay: number;

  /** 是否支持取消 */
  protected cancelable: boolean;

  /** 是否支持进度报告 */
  protected progressReporting: boolean;

  /** 当前状态 */
  protected state: AsyncNodeState = AsyncNodeState.IDLE;

  /** 开始时间 */
  protected startTime: number = 0;

  /** 当前重试次数 */
  protected currentRetries: number = 0;

  /** 当前进度（0-100） */
  protected progress: number = 0;

  /** 完成回调 */
  protected completeCallback: (() => void) | null = null;

  /** 错误回调 */
  protected errorCallback: ((error: any) => void) | null = null;

  /** 取消回调 */
  protected cancelCallback: (() => void) | null = null;

  /** 进度回调 */
  protected progressCallback: ((progress: number) => void) | null = null;

  /** 超时定时器ID */
  protected timeoutId: any = null;

  /** 取消控制器 */
  protected abortController: AbortController | null = null;
  
  /**
   * 创建异步节点
   * @param options 节点选项
   */
  constructor(options: AsyncNodeOptions) {
    super(options);

    this.timeout = options.timeout || 0;
    this.maxRetries = options.maxRetries || 0;
    this.retryDelay = options.retryDelay || 1000;
    this.cancelable = options.cancelable || false;
    this.progressReporting = options.progressReporting || false;

    // 添加完成和错误输出流程
    if (!this.outputFlowNames.includes('complete')) {
      this.outputFlowNames.push('complete');
    }

    if (!this.outputFlowNames.includes('error')) {
      this.outputFlowNames.push('error');
    }

    // 如果支持取消，添加取消输出流程
    if (this.cancelable && !this.outputFlowNames.includes('canceled')) {
      this.outputFlowNames.push('canceled');
    }
  }
  
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加状态输出
    this.addOutput({
      name: 'state',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '当前状态'
    });

    // 添加错误输出
    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'object',
      description: '错误信息'
    });

    // 如果支持进度报告，添加进度输出
    if (this.progressReporting) {
      this.addOutput({
        name: 'progress',
        type: SocketType.DATA,
        direction: SocketDirection.OUTPUT,
        dataType: 'number',
        description: '当前进度（0-100）'
      });
    }

    // 如果支持取消，添加取消输入
    if (this.cancelable) {
      this.addInput({
        name: 'cancel',
        type: SocketType.FLOW,
        direction: SocketDirection.INPUT,
        description: '取消操作'
      });
    }
  }
  
  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 检查是否是取消输入
    if (this.cancelable) {
      const cancelConnection = this.inputConnections.get('cancel');
      if (cancelConnection) {
        this.cancel();
        return null;
      }
    }

    // 如果已经在运行，不重复执行
    if (this.state === AsyncNodeState.RUNNING) {
      return null;
    }

    // 获取所有输入值
    const inputs: Record<string, any> = {};

    for (const [name, socket] of this.inputs.entries()) {
      if (socket.type === SocketType.DATA) {
        inputs[name] = this.getInputValue(name);
      }
    }

    // 开始异步操作
    this.start(inputs);

    // 不触发任何输出流程，等待异步操作完成
    return null;
  }
  
  /**
   * 开始异步操作
   * @param inputs 输入值
   */
  protected start(inputs: Record<string, any>): void {
    // 重置状态
    this.currentRetries = 0;
    this.progress = 0;

    // 执行异步操作（带重试）
    this.executeWithRetry(inputs);
  }

  /**
   * 执行异步操作（带重试）
   * @param inputs 输入值
   */
  protected executeWithRetry(inputs: Record<string, any>): void {
    // 设置状态为运行中
    this.state = AsyncNodeState.RUNNING;
    this.setOutputValue('state', this.state);

    // 记录开始时间
    this.startTime = Date.now();

    // 创建取消控制器
    if (this.cancelable) {
      this.abortController = new AbortController();
    }

    // 设置超时定时器
    if (this.timeout > 0) {
      this.timeoutId = setTimeout(() => {
        this.handleError(new Error('操作超时'));
      }, this.timeout);
    }

    // 执行异步操作
    try {
      const asyncResult = this.executeAsync(inputs);

      // 确保返回的是 Promise
      if (asyncResult && typeof asyncResult.then === 'function') {
        asyncResult
          .then(result => this.handleComplete(result))
          .catch(error => this.handleRetryOrError(error, inputs));
      } else {
        // 如果不是 Promise，直接处理结果
        this.handleComplete(asyncResult);
      }
    } catch (error) {
      this.handleRetryOrError(error, inputs);
    }
  }
  
  /**
   * 执行异步操作
   * @param inputs 输入值
   * @returns Promise对象
   */
  protected async executeAsync(_inputs: Record<string, any>): Promise<any> {
    // 子类实现具体的异步逻辑
    return null;
  }

  /**
   * 检查操作是否应该被取消
   * @returns 是否应该取消
   */
  protected shouldCancel(): boolean {
    return this.state !== AsyncNodeState.RUNNING ||
           (this.abortController?.signal.aborted ?? false);
  }

  /**
   * 处理重试或错误
   * @param error 错误
   * @param inputs 输入值
   */
  protected handleRetryOrError(error: any, inputs: Record<string, any>): void {
    // 如果还有重试次数，进行重试
    if (this.currentRetries < this.maxRetries) {
      this.currentRetries++;

      // 延迟后重试
      setTimeout(() => {
        if (this.state === AsyncNodeState.RUNNING) {
          this.executeWithRetry(inputs);
        }
      }, this.retryDelay);
    } else {
      // 没有重试次数了，处理错误
      this.handleError(error);
    }
  }
  
  /**
   * 处理完成
   * @param result 结果
   */
  protected handleComplete(result: any): void {
    // 如果不在运行状态，忽略完成事件
    if (this.state !== AsyncNodeState.RUNNING) {
      return;
    }

    // 清除超时定时器
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }

    // 设置状态为已完成
    this.state = AsyncNodeState.COMPLETED;
    this.setOutputValue('state', this.state);

    // 设置输出值
    if (result !== null && result !== undefined) {
      try {
        // 如果结果是对象，分别设置各个输出
        if (typeof result === 'object' && !Array.isArray(result)) {
          for (const [key, value] of Object.entries(result)) {
            if (this.outputs.has(key) && key !== 'state' && key !== 'error') {
              this.setOutputValue(key, value);
            }
          }
        }
        // 如果只有一个数据输出，直接设置
        else {
          const dataOutputs = Array.from(this.outputs.entries())
            .filter(([name, socket]) => socket.type === SocketType.DATA && name !== 'state' && name !== 'error');

          if (dataOutputs.length === 1) {
            this.setOutputValue(dataOutputs[0][0], result);
          } else if (dataOutputs.length > 1) {
            // 如果有多个数据输出但结果不是对象，设置第一个输出
            this.setOutputValue(dataOutputs[0][0], result);
          }
        }
      } catch (error) {
        console.warn('设置异步节点输出值时出错:', error);
      }
    }

    // 触发完成流程
    this.triggerFlow('complete');

    // 调用完成回调
    if (this.completeCallback) {
      try {
        this.completeCallback();
      } catch (error) {
        console.warn('执行异步节点完成回调时出错:', error);
      }
      this.completeCallback = null;
    }
  }
  
  /**
   * 处理错误
   * @param error 错误
   */
  protected handleError(error: any): void {
    // 如果不在运行状态，忽略错误事件
    if (this.state !== AsyncNodeState.RUNNING) {
      return;
    }

    // 清除超时定时器
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }

    // 设置状态为出错
    this.state = AsyncNodeState.ERROR;
    this.setOutputValue('state', this.state);

    // 设置错误输出
    this.setOutputValue('error', error);

    // 触发错误流程
    this.triggerFlow('error');

    // 调用错误回调
    if (this.errorCallback) {
      try {
        this.errorCallback(error);
      } catch (callbackError) {
        console.warn('执行异步节点错误回调时出错:', callbackError);
      }
      this.errorCallback = null;
    }
  }
  
  /**
   * 取消操作
   */
  public cancel(): void {
    // 如果不在运行状态，不需要取消
    if (this.state !== AsyncNodeState.RUNNING) {
      return;
    }

    // 清除超时定时器
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }

    // 取消正在进行的操作
    if (this.abortController) {
      this.abortController.abort();
      this.abortController = null;
    }

    // 设置状态为已取消
    this.state = AsyncNodeState.CANCELED;
    this.setOutputValue('state', this.state);

    // 触发取消流程
    if (this.cancelable) {
      this.triggerFlow('canceled');
    }

    // 调用取消回调
    if (this.cancelCallback) {
      try {
        this.cancelCallback();
      } catch (error) {
        console.warn('执行异步节点取消回调时出错:', error);
      }
      this.cancelCallback = null;
    }
  }
  
  /**
   * 重置状态
   */
  public reset(): void {
    // 如果在运行状态，先取消
    if (this.state === AsyncNodeState.RUNNING) {
      this.cancel();
    }

    // 重置状态
    this.state = AsyncNodeState.IDLE;
    this.setOutputValue('state', this.state);
    this.setOutputValue('error', null);

    // 重置进度
    this.progress = 0;
    this.currentRetries = 0;
    if (this.progressReporting) {
      this.setOutputValue('progress', this.progress);
    }

    // 清除回调
    this.completeCallback = null;
    this.errorCallback = null;
    this.cancelCallback = null;
    this.progressCallback = null;
  }
  
  /**
   * 设置完成回调
   * @param callback 回调函数
   */
  public onComplete(callback: () => void): void {
    this.completeCallback = callback;
  }
  
  /**
   * 设置错误回调
   * @param callback 回调函数
   */
  public onError(callback: (error: any) => void): void {
    this.errorCallback = callback;
  }
  
  /**
   * 设置取消回调
   * @param callback 回调函数
   */
  public onCancel(callback: () => void): void {
    this.cancelCallback = callback;
  }

  /**
   * 设置进度回调
   * @param callback 回调函数
   */
  public onProgress(callback: (progress: number) => void): void {
    this.progressCallback = callback;
  }
  
  /**
   * 获取当前状态
   * @returns 当前状态
   */
  public getState(): AsyncNodeState {
    return this.state;
  }
  
  /**
   * 获取运行时间（毫秒）
   * @returns 运行时间
   */
  public getRunningTime(): number {
    if (this.state !== AsyncNodeState.RUNNING || this.startTime === 0) {
      return 0;
    }

    return Date.now() - this.startTime;
  }

  /**
   * 更新进度
   * @param progress 进度值（0-100）
   */
  protected updateProgress(progress: number): void {
    if (!this.progressReporting) {
      return;
    }

    this.progress = Math.max(0, Math.min(100, progress));
    this.setOutputValue('progress', this.progress);

    // 调用进度回调
    if (this.progressCallback) {
      try {
        this.progressCallback(this.progress);
      } catch (error) {
        console.warn('执行异步节点进度回调时出错:', error);
      }
    }
  }

  /**
   * 获取当前进度
   * @returns 当前进度（0-100）
   */
  public getProgress(): number {
    return this.progress;
  }

  /**
   * 获取当前重试次数
   * @returns 当前重试次数
   */
  public getCurrentRetries(): number {
    return this.currentRetries;
  }

  /**
   * 获取最大重试次数
   * @returns 最大重试次数
   */
  public getMaxRetries(): number {
    return this.maxRetries;
  }

  /**
   * 是否支持取消
   * @returns 是否支持取消
   */
  public isCancelable(): boolean {
    return this.cancelable;
  }

  /**
   * 是否支持进度报告
   * @returns 是否支持进度报告
   */
  public isProgressReporting(): boolean {
    return this.progressReporting;
  }

  /**
   * 获取取消控制器
   * @returns 取消控制器
   */
  protected getAbortController(): AbortController | null {
    return this.abortController;
  }
}
