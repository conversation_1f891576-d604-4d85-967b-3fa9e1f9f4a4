/**
 * 视觉脚本事件节点
 * 事件节点是视觉脚本的入口点，用于响应各种事件
 */
import { Node, NodeCategory, NodeOptions, NodeType, SocketType, SocketDirection } from './Node';

/**
 * 事件类型枚举
 */
export enum EventType {
  START = 'start',
  UPDATE = 'update',
  FIXED_UPDATE = 'fixedUpdate',
  LATE_UPDATE = 'lateUpdate',
  MOUSE_DOWN = 'mouseDown',
  MOUSE_UP = 'mouseUp',
  MOUSE_MOVE = 'mouseMove',
  MOUSE_ENTER = 'mouseEnter',
  MOUSE_LEAVE = 'mouseLeave',
  KEY_DOWN = 'keyDown',
  KEY_UP = 'keyUp',
  COLLISION_ENTER = 'collisionEnter',
  COLLISION_EXIT = 'collisionExit',
  TRIGGER_ENTER = 'triggerEnter',
  TRIGGER_EXIT = 'triggerExit',
  CUSTOM = 'custom'
}

/**
 * 事件节点选项
 */
export interface EventNodeOptions extends NodeOptions {
  /** 事件名称 */
  eventName?: string;
}

/**
 * 事件节点基类
 */
export class EventNode extends Node {
  /** 节点类型 */
  public readonly nodeType: NodeType = NodeType.EVENT;
  
  /** 节点类别 */
  public readonly category: NodeCategory = NodeCategory.EVENT;
  
  /** 事件名称 */
  protected eventName: string;
  
  /**
   * 创建事件节点
   * @param options 节点选项
   */
  constructor(options: EventNodeOptions) {
    super(options);
    
    this.eventName = options.eventName || '';
  }
  
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 事件节点只有输出流程插槽，没有输入流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '当事件触发时执行'
    });
  }
  
  /**
   * 初始化节点
   * 在视觉脚本引擎启动时调用
   */
  public initialize(): void {
    // 确保插槽已初始化
    if (this.outputs.size === 0) {
      this.initializeSockets();
    }

    // 子类可以重写此方法来添加自定义初始化逻辑
  }

  /**
   * 当视觉脚本开始执行时调用
   */
  public onStart(): void {
    // 子类实现具体的启动逻辑
  }

  /**
   * 当视觉脚本停止执行时调用
   */
  public onStop(): void {
    // 子类实现具体的停止逻辑
  }
  
  /**
   * 当视觉脚本更新时调用
   * @param _deltaTime 帧间隔时间（秒）
   */
  public onUpdate(_deltaTime: number): void {
    // 子类实现具体的更新逻辑
  }

  /**
   * 获取事件名称
   * @returns 事件名称
   */
  public getEventName(): string {
    return this.eventName;
  }

  /**
   * 设置事件名称
   * @param eventName 事件名称
   */
  public setEventName(eventName: string): void {
    this.eventName = eventName;
  }

  /**
   * 触发事件
   * @param args 事件参数
   */
  protected trigger(...args: any[]): void {
    try {
      // 设置输出参数
      if (args.length > 0 && this.outputs.size > 1) {
        const outputNames = Array.from(this.outputs.keys())
          .filter(name => name !== 'flow')
          .sort(); // 确保输出顺序的一致性

        for (let i = 0; i < Math.min(args.length, outputNames.length); i++) {
          this.setOutputValue(outputNames[i], args[i]);
        }
      }

      // 触发流程
      this.triggerFlow('flow');
    } catch (error) {
      console.error('触发事件节点时出错:', error);
      // 即使出错也要尝试触发流程，以免中断执行
      try {
        this.triggerFlow('flow');
      } catch (flowError) {
        console.error('触发事件节点流程时出错:', flowError);
      }
    }
  }
}

/**
 * 开始事件节点
 * 当视觉脚本开始执行时触发
 */
export class StartEventNode extends EventNode {
  constructor(options: EventNodeOptions) {
    super({
      ...options,
      eventName: EventType.START
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '开始';
    }
    if (!this.metadata.description) {
      this.metadata.description = '当视觉脚本开始执行时触发';
    }
  }

  public onStart(): void {
    // 在脚本开始时立即触发
    this.trigger();
  }
}

/**
 * 更新事件节点
 * 每帧更新时触发
 */
export class UpdateEventNode extends EventNode {
  constructor(options: EventNodeOptions) {
    super({
      ...options,
      eventName: EventType.UPDATE
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '更新';
    }
    if (!this.metadata.description) {
      this.metadata.description = '每帧更新时触发';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加deltaTime输出
    this.addOutput({
      name: 'deltaTime',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '帧间隔时间（秒）'
    });
  }

  public onUpdate(deltaTime: number): void {
    this.trigger(deltaTime);
  }
}

/**
 * 固定更新事件节点
 * 固定时间步长更新时触发
 */
export class FixedUpdateEventNode extends EventNode {
  constructor(options: EventNodeOptions) {
    super({
      ...options,
      eventName: EventType.FIXED_UPDATE
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '固定更新';
    }
    if (!this.metadata.description) {
      this.metadata.description = '固定时间步长更新时触发';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加fixedDeltaTime输出
    this.addOutput({
      name: 'fixedDeltaTime',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '固定帧间隔时间（秒）'
    });
  }

  public onFixedUpdate(fixedDeltaTime: number): void {
    this.trigger(fixedDeltaTime);
  }
}

/**
 * 后更新事件节点
 * 后更新时触发
 */
export class LateUpdateEventNode extends EventNode {
  constructor(options: EventNodeOptions) {
    super({
      ...options,
      eventName: EventType.LATE_UPDATE
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '后更新';
    }
    if (!this.metadata.description) {
      this.metadata.description = '后更新时触发';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加deltaTime输出
    this.addOutput({
      name: 'deltaTime',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '帧间隔时间（秒）'
    });
  }

  public onLateUpdate(deltaTime: number): void {
    this.trigger(deltaTime);
  }
}

/**
 * 鼠标按下事件节点
 */
export class MouseDownEventNode extends EventNode {
  constructor(options: EventNodeOptions) {
    super({
      ...options,
      eventName: EventType.MOUSE_DOWN
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '鼠标按下';
    }
    if (!this.metadata.description) {
      this.metadata.description = '鼠标按下时触发';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加鼠标位置输出
    this.addOutput({
      name: 'mouseX',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '鼠标X坐标'
    });

    this.addOutput({
      name: 'mouseY',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '鼠标Y坐标'
    });

    this.addOutput({
      name: 'button',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '鼠标按键（0=左键，1=中键，2=右键）'
    });
  }

  public onMouseDown(event: MouseEvent): void {
    this.trigger(event.clientX, event.clientY, event.button);
  }
}

/**
 * 鼠标抬起事件节点
 */
export class MouseUpEventNode extends EventNode {
  constructor(options: EventNodeOptions) {
    super({
      ...options,
      eventName: EventType.MOUSE_UP
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '鼠标抬起';
    }
    if (!this.metadata.description) {
      this.metadata.description = '鼠标抬起时触发';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加鼠标位置输出
    this.addOutput({
      name: 'mouseX',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '鼠标X坐标'
    });

    this.addOutput({
      name: 'mouseY',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '鼠标Y坐标'
    });

    this.addOutput({
      name: 'button',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '鼠标按键（0=左键，1=中键，2=右键）'
    });
  }

  public onMouseUp(event: MouseEvent): void {
    this.trigger(event.clientX, event.clientY, event.button);
  }
}

/**
 * 鼠标移动事件节点
 */
export class MouseMoveEventNode extends EventNode {
  constructor(options: EventNodeOptions) {
    super({
      ...options,
      eventName: EventType.MOUSE_MOVE
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '鼠标移动';
    }
    if (!this.metadata.description) {
      this.metadata.description = '鼠标移动时触发';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加鼠标位置输出
    this.addOutput({
      name: 'mouseX',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '鼠标X坐标'
    });

    this.addOutput({
      name: 'mouseY',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '鼠标Y坐标'
    });

    this.addOutput({
      name: 'deltaX',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '鼠标X轴移动距离'
    });

    this.addOutput({
      name: 'deltaY',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '鼠标Y轴移动距离'
    });
  }

  public onMouseMove(event: MouseEvent): void {
    this.trigger(event.clientX, event.clientY, event.movementX, event.movementY);
  }
}

/**
 * 键盘按下事件节点
 */
export class KeyDownEventNode extends EventNode {
  constructor(options: EventNodeOptions) {
    super({
      ...options,
      eventName: EventType.KEY_DOWN
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '键盘按下';
    }
    if (!this.metadata.description) {
      this.metadata.description = '键盘按下时触发';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加键盘信息输出
    this.addOutput({
      name: 'key',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '按下的键'
    });

    this.addOutput({
      name: 'code',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '键码'
    });

    this.addOutput({
      name: 'ctrlKey',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否按下Ctrl键'
    });

    this.addOutput({
      name: 'shiftKey',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否按下Shift键'
    });

    this.addOutput({
      name: 'altKey',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否按下Alt键'
    });
  }

  public onKeyDown(event: KeyboardEvent): void {
    this.trigger(event.key, event.code, event.ctrlKey, event.shiftKey, event.altKey);
  }
}

/**
 * 键盘抬起事件节点
 */
export class KeyUpEventNode extends EventNode {
  constructor(options: EventNodeOptions) {
    super({
      ...options,
      eventName: EventType.KEY_UP
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '键盘抬起';
    }
    if (!this.metadata.description) {
      this.metadata.description = '键盘抬起时触发';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加键盘信息输出
    this.addOutput({
      name: 'key',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '抬起的键'
    });

    this.addOutput({
      name: 'code',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '键码'
    });

    this.addOutput({
      name: 'ctrlKey',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否按下Ctrl键'
    });

    this.addOutput({
      name: 'shiftKey',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否按下Shift键'
    });

    this.addOutput({
      name: 'altKey',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否按下Alt键'
    });
  }

  public onKeyUp(event: KeyboardEvent): void {
    this.trigger(event.key, event.code, event.ctrlKey, event.shiftKey, event.altKey);
  }
}

/**
 * 碰撞进入事件节点
 */
export class CollisionEnterEventNode extends EventNode {
  constructor(options: EventNodeOptions) {
    super({
      ...options,
      eventName: EventType.COLLISION_ENTER
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '碰撞进入';
    }
    if (!this.metadata.description) {
      this.metadata.description = '物体碰撞进入时触发';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加碰撞信息输出
    this.addOutput({
      name: 'otherEntity',
      type: SocketType.DATA,
      dataType: 'entity',
      direction: SocketDirection.OUTPUT,
      description: '碰撞的其他实体'
    });

    this.addOutput({
      name: 'contactPoint',
      type: SocketType.DATA,
      dataType: 'vector3',
      direction: SocketDirection.OUTPUT,
      description: '碰撞点'
    });

    this.addOutput({
      name: 'contactNormal',
      type: SocketType.DATA,
      dataType: 'vector3',
      direction: SocketDirection.OUTPUT,
      description: '碰撞法线'
    });

    this.addOutput({
      name: 'impulse',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '碰撞冲量'
    });
  }

  public onCollisionEnter(otherEntity: any, contactPoint: any, contactNormal: any, impulse: number): void {
    this.trigger(otherEntity, contactPoint, contactNormal, impulse);
  }
}

/**
 * 碰撞退出事件节点
 */
export class CollisionExitEventNode extends EventNode {
  constructor(options: EventNodeOptions) {
    super({
      ...options,
      eventName: EventType.COLLISION_EXIT
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '碰撞退出';
    }
    if (!this.metadata.description) {
      this.metadata.description = '物体碰撞退出时触发';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加碰撞信息输出
    this.addOutput({
      name: 'otherEntity',
      type: SocketType.DATA,
      dataType: 'entity',
      direction: SocketDirection.OUTPUT,
      description: '碰撞的其他实体'
    });
  }

  public onCollisionExit(otherEntity: any): void {
    this.trigger(otherEntity);
  }
}

/**
 * 触发器进入事件节点
 */
export class TriggerEnterEventNode extends EventNode {
  constructor(options: EventNodeOptions) {
    super({
      ...options,
      eventName: EventType.TRIGGER_ENTER
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '触发器进入';
    }
    if (!this.metadata.description) {
      this.metadata.description = '物体进入触发器时触发';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加触发器信息输出
    this.addOutput({
      name: 'otherEntity',
      type: SocketType.DATA,
      dataType: 'entity',
      direction: SocketDirection.OUTPUT,
      description: '进入触发器的实体'
    });

    this.addOutput({
      name: 'triggerEntity',
      type: SocketType.DATA,
      dataType: 'entity',
      direction: SocketDirection.OUTPUT,
      description: '触发器实体'
    });
  }

  public onTriggerEnter(otherEntity: any, triggerEntity: any): void {
    this.trigger(otherEntity, triggerEntity);
  }
}

/**
 * 触发器退出事件节点
 */
export class TriggerExitEventNode extends EventNode {
  constructor(options: EventNodeOptions) {
    super({
      ...options,
      eventName: EventType.TRIGGER_EXIT
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '触发器退出';
    }
    if (!this.metadata.description) {
      this.metadata.description = '物体退出触发器时触发';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加触发器信息输出
    this.addOutput({
      name: 'otherEntity',
      type: SocketType.DATA,
      dataType: 'entity',
      direction: SocketDirection.OUTPUT,
      description: '退出触发器的实体'
    });

    this.addOutput({
      name: 'triggerEntity',
      type: SocketType.DATA,
      dataType: 'entity',
      direction: SocketDirection.OUTPUT,
      description: '触发器实体'
    });
  }

  public onTriggerExit(otherEntity: any, triggerEntity: any): void {
    this.trigger(otherEntity, triggerEntity);
  }
}

/**
 * 自定义事件节点
 */
export class CustomEventNode extends EventNode {
  private customEventName: string;

  constructor(options: EventNodeOptions & { customEventName: string }) {
    super({
      ...options,
      eventName: EventType.CUSTOM
    });

    this.customEventName = options.customEventName;

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = `自定义事件: ${this.customEventName}`;
    }
    if (!this.metadata.description) {
      this.metadata.description = `自定义事件 "${this.customEventName}" 触发时执行`;
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加通用数据输出
    this.addOutput({
      name: 'eventData',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.OUTPUT,
      description: '事件数据'
    });
  }

  public onCustomEvent(eventData: any): void {
    this.trigger(eventData);
  }

  public getCustomEventName(): string {
    return this.customEventName;
  }
}

/**
 * 事件节点工厂
 */
export class EventNodeFactory {
  /**
   * 创建事件节点
   * @param eventType 事件类型
   * @param options 节点选项
   * @returns 事件节点实例
   */
  public static createEventNode(eventType: EventType, options: EventNodeOptions): EventNode {
    switch (eventType) {
      case EventType.START:
        return new StartEventNode(options);

      case EventType.UPDATE:
        return new UpdateEventNode(options);

      case EventType.FIXED_UPDATE:
        return new FixedUpdateEventNode(options);

      case EventType.LATE_UPDATE:
        return new LateUpdateEventNode(options);

      case EventType.MOUSE_DOWN:
        return new MouseDownEventNode(options);

      case EventType.MOUSE_UP:
        return new MouseUpEventNode(options);

      case EventType.MOUSE_MOVE:
        return new MouseMoveEventNode(options);

      case EventType.KEY_DOWN:
        return new KeyDownEventNode(options);

      case EventType.KEY_UP:
        return new KeyUpEventNode(options);

      case EventType.COLLISION_ENTER:
        return new CollisionEnterEventNode(options);

      case EventType.COLLISION_EXIT:
        return new CollisionExitEventNode(options);

      case EventType.TRIGGER_ENTER:
        return new TriggerEnterEventNode(options);

      case EventType.TRIGGER_EXIT:
        return new TriggerExitEventNode(options);

      case EventType.CUSTOM:
        // 自定义事件需要额外的参数
        throw new Error('自定义事件节点需要使用 createCustomEventNode 方法创建');

      default:
        throw new Error(`不支持的事件类型: ${eventType}`);
    }
  }

  /**
   * 创建自定义事件节点
   * @param customEventName 自定义事件名称
   * @param options 节点选项
   * @returns 自定义事件节点实例
   */
  public static createCustomEventNode(customEventName: string, options: EventNodeOptions): CustomEventNode {
    return new CustomEventNode({
      ...options,
      customEventName
    });
  }

  /**
   * 获取所有支持的事件类型
   * @returns 事件类型数组
   */
  public static getSupportedEventTypes(): EventType[] {
    return Object.values(EventType);
  }

  /**
   * 获取事件类型的显示名称
   * @param eventType 事件类型
   * @returns 显示名称
   */
  public static getEventTypeDisplayName(eventType: EventType): string {
    const displayNames: Record<EventType, string> = {
      [EventType.START]: '开始',
      [EventType.UPDATE]: '更新',
      [EventType.FIXED_UPDATE]: '固定更新',
      [EventType.LATE_UPDATE]: '后更新',
      [EventType.MOUSE_DOWN]: '鼠标按下',
      [EventType.MOUSE_UP]: '鼠标抬起',
      [EventType.MOUSE_MOVE]: '鼠标移动',
      [EventType.MOUSE_ENTER]: '鼠标进入',
      [EventType.MOUSE_LEAVE]: '鼠标离开',
      [EventType.KEY_DOWN]: '键盘按下',
      [EventType.KEY_UP]: '键盘抬起',
      [EventType.COLLISION_ENTER]: '碰撞进入',
      [EventType.COLLISION_EXIT]: '碰撞退出',
      [EventType.TRIGGER_ENTER]: '触发器进入',
      [EventType.TRIGGER_EXIT]: '触发器退出',
      [EventType.CUSTOM]: '自定义事件'
    };

    return displayNames[eventType] || eventType;
  }

  /**
   * 获取事件类型的描述
   * @param eventType 事件类型
   * @returns 描述
   */
  public static getEventTypeDescription(eventType: EventType): string {
    const descriptions: Record<EventType, string> = {
      [EventType.START]: '当视觉脚本开始执行时触发',
      [EventType.UPDATE]: '每帧更新时触发',
      [EventType.FIXED_UPDATE]: '固定时间步长更新时触发',
      [EventType.LATE_UPDATE]: '后更新时触发',
      [EventType.MOUSE_DOWN]: '鼠标按下时触发',
      [EventType.MOUSE_UP]: '鼠标抬起时触发',
      [EventType.MOUSE_MOVE]: '鼠标移动时触发',
      [EventType.MOUSE_ENTER]: '鼠标进入时触发',
      [EventType.MOUSE_LEAVE]: '鼠标离开时触发',
      [EventType.KEY_DOWN]: '键盘按下时触发',
      [EventType.KEY_UP]: '键盘抬起时触发',
      [EventType.COLLISION_ENTER]: '物体碰撞进入时触发',
      [EventType.COLLISION_EXIT]: '物体碰撞退出时触发',
      [EventType.TRIGGER_ENTER]: '物体进入触发器时触发',
      [EventType.TRIGGER_EXIT]: '物体退出触发器时触发',
      [EventType.CUSTOM]: '自定义事件触发时执行'
    };

    return descriptions[eventType] || '未知事件类型';
  }
}
