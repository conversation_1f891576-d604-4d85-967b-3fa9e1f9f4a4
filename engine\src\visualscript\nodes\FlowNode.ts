/**
 * 视觉脚本流程节点
 * 流程节点用于控制执行流程
 */
import { Node, NodeCategory, NodeOptions, NodeType, SocketDirection, SocketType } from './Node';

/**
 * 流程节点选项
 */
export interface FlowNodeOptions extends NodeOptions {
  /** 输入流程插槽名称 */
  inputFlowName?: string;
  /** 输出流程插槽名称列表 */
  outputFlowNames?: string[];
}

/**
 * 流程节点基类
 */
export class FlowNode extends Node {
  /** 节点类型 */
  public readonly nodeType: NodeType = NodeType.NORMAL;
  
  /** 节点类别 */
  public readonly category: NodeCategory = NodeCategory.FLOW;
  
  /** 输入流程插槽名称 */
  protected inputFlowName: string;
  
  /** 输出流程插槽名称列表 */
  protected outputFlowNames: string[];
  
  /**
   * 创建流程节点
   * @param options 节点选项
   */
  constructor(options: FlowNodeOptions) {
    super(options);
    
    this.inputFlowName = options.inputFlowName || 'flow';
    this.outputFlowNames = options.outputFlowNames || ['flow'];
  }
  
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: this.inputFlowName,
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });
    
    // 添加输出流程插槽
    for (const name of this.outputFlowNames) {
      this.addOutput({
        name: name,
        type: SocketType.FLOW,
        direction: SocketDirection.OUTPUT,
        description: '执行输出'
      });
    }
  }
  
  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取所有输入值
    const inputs: Record<string, any> = {};
    
    for (const [name, socket] of this.inputs.entries()) {
      if (socket.type === SocketType.DATA) {
        inputs[name] = this.getInputValue(name);
      }
    }
    
    // 处理输入并确定输出流程
    const outputFlowName = this.process(inputs);
    
    // 如果有输出流程，触发它
    if (outputFlowName && this.outputs.has(outputFlowName)) {
      this.triggerFlow(outputFlowName);
    }
    
    return outputFlowName;
  }
  
  /**
   * 处理输入并确定输出流程
   * @param inputs 输入值
   * @returns 输出流程名称
   */
  protected process(inputs: Record<string, any>): string | null {
    // 默认返回第一个输出流程
    return this.outputFlowNames.length > 0 ? this.outputFlowNames[0] : null;
  }
}

/**
 * 分支节点（If节点）
 * 根据条件决定执行流程
 */
export class BranchNode extends FlowNode {
  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['true', 'false']
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '分支';
    }
    if (!this.metadata.description) {
      this.metadata.description = '根据条件决定执行流程';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加条件输入
    this.addInput({
      name: 'condition',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '条件',
      defaultValue: false
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    const condition = inputs.condition;
    return condition ? 'true' : 'false';
  }
}

/**
 * 序列节点
 * 按顺序执行多个流程
 */
export class SequenceNode extends FlowNode {
  private currentIndex: number = 0;
  private executionCount: number = 0;

  constructor(options: FlowNodeOptions & { sequenceCount?: number }) {
    const sequenceCount = options.sequenceCount || 2;
    const outputNames = Array.from({ length: sequenceCount }, (_, i) => `then${i + 1}`);

    super({
      ...options,
      outputFlowNames: outputNames
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '序列';
    }
    if (!this.metadata.description) {
      this.metadata.description = '按顺序执行多个流程';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加重置输入
    this.addInput({
      name: 'reset',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '重置序列'
    });
  }

  public execute(): any {
    // 检查是否是重置输入
    const resetConnection = this.inputConnections.get('reset');
    if (resetConnection) {
      this.currentIndex = 0;
      this.executionCount = 0;
      return null;
    }

    // 执行当前序列
    if (this.currentIndex < this.outputFlowNames.length) {
      const outputFlowName = this.outputFlowNames[this.currentIndex];
      this.currentIndex++;
      this.executionCount++;

      // 如果到达末尾，重置索引
      if (this.currentIndex >= this.outputFlowNames.length) {
        this.currentIndex = 0;
      }

      this.triggerFlow(outputFlowName);
      return outputFlowName;
    }

    return null;
  }

  /**
   * 重置序列
   */
  public reset(): void {
    this.currentIndex = 0;
    this.executionCount = 0;
  }

  /**
   * 获取当前执行索引
   */
  public getCurrentIndex(): number {
    return this.currentIndex;
  }

  /**
   * 获取执行次数
   */
  public getExecutionCount(): number {
    return this.executionCount;
  }
}

/**
 * 选择器节点（Switch节点）
 * 根据索引选择执行流程
 */
export class SelectorNode extends FlowNode {
  constructor(options: FlowNodeOptions & { optionCount?: number }) {
    const optionCount = options.optionCount || 3;
    const outputNames = Array.from({ length: optionCount }, (_, i) => `option${i}`);
    outputNames.push('default'); // 添加默认输出

    super({
      ...options,
      outputFlowNames: outputNames
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '选择器';
    }
    if (!this.metadata.description) {
      this.metadata.description = '根据索引选择执行流程';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加选择索引输入
    this.addInput({
      name: 'index',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '选择索引',
      defaultValue: 0
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    const index = Math.floor(inputs.index || 0);
    const optionName = `option${index}`;

    // 检查索引是否有效
    if (index >= 0 && index < this.outputFlowNames.length - 1 && this.outputs.has(optionName)) {
      return optionName;
    }

    // 返回默认输出
    return 'default';
  }
}

/**
 * 循环节点（For Loop节点）
 * 执行指定次数的循环
 */
export class ForLoopNode extends FlowNode {
  private currentIndex: number = 0;
  private isLooping: boolean = false;

  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['loopBody', 'completed']
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '循环';
    }
    if (!this.metadata.description) {
      this.metadata.description = '执行指定次数的循环';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加循环参数输入
    this.addInput({
      name: 'startIndex',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '起始索引',
      defaultValue: 0
    });

    this.addInput({
      name: 'endIndex',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '结束索引',
      defaultValue: 10
    });

    this.addInput({
      name: 'step',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '步长',
      defaultValue: 1
    });

    // 添加循环体完成输入
    this.addInput({
      name: 'loopBodyCompleted',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '循环体完成'
    });

    // 添加当前索引输出
    this.addOutput({
      name: 'currentIndex',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '当前索引'
    });
  }

  public execute(): any {
    // 检查是否是循环体完成输入
    const loopBodyConnection = this.inputConnections.get('loopBodyCompleted');
    if (loopBodyConnection && this.isLooping) {
      return this.continueLoop();
    }

    // 开始新的循环
    return this.startLoop();
  }

  private startLoop(): string | null {
    const inputs = this.getInputValues();
    const startIndex = inputs.startIndex || 0;
    const endIndex = inputs.endIndex || 10;
    const step = inputs.step || 1;

    this.currentIndex = startIndex;
    this.isLooping = true;

    // 检查循环条件
    if ((step > 0 && this.currentIndex < endIndex) ||
        (step < 0 && this.currentIndex > endIndex)) {
      this.setOutputValue('currentIndex', this.currentIndex);
      return 'loopBody';
    } else {
      this.isLooping = false;
      return 'completed';
    }
  }

  private continueLoop(): string | null {
    const inputs = this.getInputValues();
    const endIndex = inputs.endIndex || 10;
    const step = inputs.step || 1;

    this.currentIndex += step;

    // 检查循环条件
    if ((step > 0 && this.currentIndex < endIndex) ||
        (step < 0 && this.currentIndex > endIndex)) {
      this.setOutputValue('currentIndex', this.currentIndex);
      return 'loopBody';
    } else {
      this.isLooping = false;
      return 'completed';
    }
  }

  private getInputValues(): Record<string, any> {
    const inputs: Record<string, any> = {};
    for (const [name, socket] of this.inputs.entries()) {
      if (socket.type === SocketType.DATA) {
        inputs[name] = this.getInputValue(name);
      }
    }
    return inputs;
  }

  /**
   * 重置循环
   */
  public reset(): void {
    this.currentIndex = 0;
    this.isLooping = false;
  }

  /**
   * 获取当前索引
   */
  public getCurrentIndex(): number {
    return this.currentIndex;
  }

  /**
   * 是否正在循环
   */
  public getIsLooping(): boolean {
    return this.isLooping;
  }
}

/**
 * While循环节点
 * 根据条件执行循环
 */
export class WhileLoopNode extends FlowNode {
  private isLooping: boolean = false;
  private iterationCount: number = 0;
  private maxIterations: number = 1000; // 防止无限循环

  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['loopBody', 'completed']
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = 'While循环';
    }
    if (!this.metadata.description) {
      this.metadata.description = '根据条件执行循环';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加条件输入
    this.addInput({
      name: 'condition',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '循环条件',
      defaultValue: false
    });

    // 添加最大迭代次数输入
    this.addInput({
      name: 'maxIterations',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '最大迭代次数',
      defaultValue: 1000
    });

    // 添加循环体完成输入
    this.addInput({
      name: 'loopBodyCompleted',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '循环体完成'
    });

    // 添加迭代次数输出
    this.addOutput({
      name: 'iterationCount',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '迭代次数'
    });
  }

  public execute(): any {
    // 检查是否是循环体完成输入
    const loopBodyConnection = this.inputConnections.get('loopBodyCompleted');
    if (loopBodyConnection && this.isLooping) {
      return this.continueLoop();
    }

    // 开始新的循环
    return this.startLoop();
  }

  private startLoop(): string | null {
    const inputs = this.getInputValues();
    const condition = inputs.condition;
    this.maxIterations = inputs.maxIterations || 1000;

    this.iterationCount = 0;
    this.isLooping = true;

    // 检查循环条件
    if (condition && this.iterationCount < this.maxIterations) {
      this.setOutputValue('iterationCount', this.iterationCount);
      return 'loopBody';
    } else {
      this.isLooping = false;
      return 'completed';
    }
  }

  private continueLoop(): string | null {
    const inputs = this.getInputValues();
    const condition = inputs.condition;

    this.iterationCount++;

    // 检查循环条件
    if (condition && this.iterationCount < this.maxIterations) {
      this.setOutputValue('iterationCount', this.iterationCount);
      return 'loopBody';
    } else {
      this.isLooping = false;
      return 'completed';
    }
  }

  private getInputValues(): Record<string, any> {
    const inputs: Record<string, any> = {};
    for (const [name, socket] of this.inputs.entries()) {
      if (socket.type === SocketType.DATA) {
        inputs[name] = this.getInputValue(name);
      }
    }
    return inputs;
  }

  /**
   * 重置循环
   */
  public reset(): void {
    this.iterationCount = 0;
    this.isLooping = false;
  }

  /**
   * 获取迭代次数
   */
  public getIterationCount(): number {
    return this.iterationCount;
  }

  /**
   * 是否正在循环
   */
  public getIsLooping(): boolean {
    return this.isLooping;
  }
}

/**
 * 延迟节点
 * 延迟指定时间后执行
 */
export class DelayNode extends FlowNode {
  private timeoutId: number | null = null;

  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['completed']
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '延迟';
    }
    if (!this.metadata.description) {
      this.metadata.description = '延迟指定时间后执行';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加延迟时间输入
    this.addInput({
      name: 'duration',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '延迟时间（秒）',
      defaultValue: 1.0
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    const duration = inputs.duration || 1.0;

    // 清除之前的定时器
    if (this.timeoutId !== null) {
      clearTimeout(this.timeoutId);
    }

    // 设置新的定时器
    this.timeoutId = window.setTimeout(() => {
      this.triggerFlow('completed');
      this.timeoutId = null;
    }, duration * 1000);

    // 不立即触发输出
    return null;
  }

  /**
   * 取消延迟
   */
  public cancel(): void {
    if (this.timeoutId !== null) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
  }

  /**
   * 是否正在延迟
   */
  public isDelaying(): boolean {
    return this.timeoutId !== null;
  }
}

/**
 * 门节点（Gate节点）
 * 控制流程的开启和关闭
 */
export class GateNode extends FlowNode {
  private isOpen: boolean = false;

  constructor(options: FlowNodeOptions) {
    super({
      ...options,
      outputFlowNames: ['output']
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '门';
    }
    if (!this.metadata.description) {
      this.metadata.description = '控制流程的开启和关闭';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加开启输入
    this.addInput({
      name: 'open',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '开启门'
    });

    // 添加关闭输入
    this.addInput({
      name: 'close',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '关闭门'
    });

    // 添加切换输入
    this.addInput({
      name: 'toggle',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '切换门状态'
    });

    // 添加门状态输出
    this.addOutput({
      name: 'isOpen',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '门是否开启'
    });
  }

  public execute(): any {
    // 检查输入类型
    const openConnection = this.inputConnections.get('open');
    const closeConnection = this.inputConnections.get('close');
    const toggleConnection = this.inputConnections.get('toggle');

    if (openConnection) {
      this.isOpen = true;
    } else if (closeConnection) {
      this.isOpen = false;
    } else if (toggleConnection) {
      this.isOpen = !this.isOpen;
    }

    // 更新门状态输出
    this.setOutputValue('isOpen', this.isOpen);

    // 如果门是开启的且有主流程输入，则触发输出
    if (this.isOpen) {
      const mainFlowConnection = this.inputConnections.get(this.inputFlowName);
      if (mainFlowConnection) {
        return 'output';
      }
    }

    return null;
  }

  /**
   * 开启门
   */
  public open(): void {
    this.isOpen = true;
    this.setOutputValue('isOpen', this.isOpen);
  }

  /**
   * 关闭门
   */
  public close(): void {
    this.isOpen = false;
    this.setOutputValue('isOpen', this.isOpen);
  }

  /**
   * 切换门状态
   */
  public toggle(): void {
    this.isOpen = !this.isOpen;
    this.setOutputValue('isOpen', this.isOpen);
  }

  /**
   * 获取门状态
   */
  public getIsOpen(): boolean {
    return this.isOpen;
  }
}

/**
 * 多重门节点（MultiGate节点）
 * 按顺序循环输出到多个流程
 */
export class MultiGateNode extends FlowNode {
  private currentIndex: number = 0;
  private isRandom: boolean = false;

  constructor(options: FlowNodeOptions & { outputCount?: number }) {
    const outputCount = options.outputCount || 2;
    const outputNames = Array.from({ length: outputCount }, (_, i) => `output${i + 1}`);

    super({
      ...options,
      outputFlowNames: outputNames
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '多重门';
    }
    if (!this.metadata.description) {
      this.metadata.description = '按顺序循环输出到多个流程';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加重置输入
    this.addInput({
      name: 'reset',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '重置到第一个输出'
    });

    // 添加随机模式输入
    this.addInput({
      name: 'isRandom',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否随机选择输出',
      defaultValue: false
    });

    // 添加当前索引输出
    this.addOutput({
      name: 'currentIndex',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '当前输出索引'
    });
  }

  public execute(): any {
    // 检查是否是重置输入
    const resetConnection = this.inputConnections.get('reset');
    if (resetConnection) {
      this.currentIndex = 0;
      this.setOutputValue('currentIndex', this.currentIndex);
      return null;
    }

    // 获取随机模式设置
    this.isRandom = this.getInputValue('isRandom') || false;

    // 选择输出
    let outputIndex: number;
    if (this.isRandom) {
      outputIndex = Math.floor(Math.random() * this.outputFlowNames.length);
    } else {
      outputIndex = this.currentIndex;
      this.currentIndex = (this.currentIndex + 1) % this.outputFlowNames.length;
    }

    // 更新当前索引输出
    this.setOutputValue('currentIndex', outputIndex);

    return this.outputFlowNames[outputIndex];
  }

  /**
   * 重置到第一个输出
   */
  public reset(): void {
    this.currentIndex = 0;
    this.setOutputValue('currentIndex', this.currentIndex);
  }

  /**
   * 获取当前索引
   */
  public getCurrentIndex(): number {
    return this.currentIndex;
  }

  /**
   * 设置随机模式
   */
  public setRandomMode(isRandom: boolean): void {
    this.isRandom = isRandom;
  }

  /**
   * 是否为随机模式
   */
  public getIsRandom(): boolean {
    return this.isRandom;
  }
}

/**
 * 流程节点类型枚举
 */
export enum FlowNodeType {
  BRANCH = 'branch',
  SEQUENCE = 'sequence',
  SELECTOR = 'selector',
  FOR_LOOP = 'forLoop',
  WHILE_LOOP = 'whileLoop',
  DELAY = 'delay',
  GATE = 'gate',
  MULTI_GATE = 'multiGate'
}

/**
 * 流程节点工厂
 */
export class FlowNodeFactory {
  /**
   * 创建流程节点
   * @param nodeType 节点类型
   * @param options 节点选项
   * @returns 流程节点实例
   */
  public static createFlowNode(nodeType: FlowNodeType, options: FlowNodeOptions): FlowNode {
    switch (nodeType) {
      case FlowNodeType.BRANCH:
        return new BranchNode(options);

      case FlowNodeType.SEQUENCE:
        return new SequenceNode(options);

      case FlowNodeType.SELECTOR:
        return new SelectorNode(options);

      case FlowNodeType.FOR_LOOP:
        return new ForLoopNode(options);

      case FlowNodeType.WHILE_LOOP:
        return new WhileLoopNode(options);

      case FlowNodeType.DELAY:
        return new DelayNode(options);

      case FlowNodeType.GATE:
        return new GateNode(options);

      case FlowNodeType.MULTI_GATE:
        return new MultiGateNode(options);

      default:
        throw new Error(`不支持的流程节点类型: ${nodeType}`);
    }
  }

  /**
   * 获取所有支持的流程节点类型
   * @returns 流程节点类型数组
   */
  public static getSupportedFlowNodeTypes(): FlowNodeType[] {
    return Object.values(FlowNodeType);
  }

  /**
   * 获取流程节点类型的显示名称
   * @param nodeType 节点类型
   * @returns 显示名称
   */
  public static getFlowNodeTypeDisplayName(nodeType: FlowNodeType): string {
    const displayNames: Record<FlowNodeType, string> = {
      [FlowNodeType.BRANCH]: '分支',
      [FlowNodeType.SEQUENCE]: '序列',
      [FlowNodeType.SELECTOR]: '选择器',
      [FlowNodeType.FOR_LOOP]: '循环',
      [FlowNodeType.WHILE_LOOP]: 'While循环',
      [FlowNodeType.DELAY]: '延迟',
      [FlowNodeType.GATE]: '门',
      [FlowNodeType.MULTI_GATE]: '多重门'
    };

    return displayNames[nodeType] || nodeType;
  }

  /**
   * 获取流程节点类型的描述
   * @param nodeType 节点类型
   * @returns 描述
   */
  public static getFlowNodeTypeDescription(nodeType: FlowNodeType): string {
    const descriptions: Record<FlowNodeType, string> = {
      [FlowNodeType.BRANCH]: '根据条件决定执行流程',
      [FlowNodeType.SEQUENCE]: '按顺序执行多个流程',
      [FlowNodeType.SELECTOR]: '根据索引选择执行流程',
      [FlowNodeType.FOR_LOOP]: '执行指定次数的循环',
      [FlowNodeType.WHILE_LOOP]: '根据条件执行循环',
      [FlowNodeType.DELAY]: '延迟指定时间后执行',
      [FlowNodeType.GATE]: '控制流程的开启和关闭',
      [FlowNodeType.MULTI_GATE]: '按顺序循环输出到多个流程'
    };

    return descriptions[nodeType] || '未知流程节点类型';
  }

  /**
   * 获取流程节点类型的默认配置
   * @param nodeType 节点类型
   * @returns 默认配置
   */
  public static getFlowNodeTypeDefaultConfig(nodeType: FlowNodeType): Partial<FlowNodeOptions> {
    const defaultConfigs: Record<FlowNodeType, Partial<FlowNodeOptions>> = {
      [FlowNodeType.BRANCH]: {
        outputFlowNames: ['true', 'false']
      },
      [FlowNodeType.SEQUENCE]: {
        outputFlowNames: ['then1', 'then2']
      },
      [FlowNodeType.SELECTOR]: {
        outputFlowNames: ['option0', 'option1', 'option2', 'default']
      },
      [FlowNodeType.FOR_LOOP]: {
        outputFlowNames: ['loopBody', 'completed']
      },
      [FlowNodeType.WHILE_LOOP]: {
        outputFlowNames: ['loopBody', 'completed']
      },
      [FlowNodeType.DELAY]: {
        outputFlowNames: ['completed']
      },
      [FlowNodeType.GATE]: {
        outputFlowNames: ['output']
      },
      [FlowNodeType.MULTI_GATE]: {
        outputFlowNames: ['output1', 'output2']
      }
    };

    return defaultConfigs[nodeType] || {};
  }
}
