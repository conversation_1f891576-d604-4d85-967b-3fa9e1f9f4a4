/**
 * 视觉脚本预设节点
 * 提供各种预设节点的注册
 */
import { NodeRegistry } from '../nodes/NodeRegistry';
import { registerMathNodes } from './MathNodes';
import { registerLogicNodes } from './LogicNodes';
import { registerEntityNodes } from './EntityNodes';
import { registerAsyncExampleNodes } from './AsyncExampleNodes';

/**
 * 注册所有预设节点
 * @param registry 节点注册表
 */
export function registerAllPresetNodes(registry: NodeRegistry): void {
  // 注册数学节点
  registerMathNodes(registry);

  // 注册逻辑节点
  registerLogicNodes(registry);

  // 注册实体节点
  registerEntityNodes(registry);

  // 注册异步示例节点
  registerAsyncExampleNodes(registry);

  // 可以在这里添加更多节点类型的注册
}

export * from './MathNodes';
export * from './LogicNodes';
export * from './EntityNodes';
export * from './AsyncExampleNodes';
