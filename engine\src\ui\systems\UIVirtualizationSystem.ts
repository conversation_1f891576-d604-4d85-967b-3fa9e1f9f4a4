/**
 * UIVirtualizationSystem.ts
 * 
 * UI虚拟化渲染系统，用于优化大量UI元素的性能
 * 支持虚拟滚动、按需渲染和视口裁剪
 */

import { System } from '../../core/System';
import { Entity } from '../../core/Entity';
import { UIComponent } from '../components/UIComponent';
import { Vector2, Box2 } from 'three';

/**
 * 虚拟化配置接口
 */
export interface VirtualizationConfig {
  /** 是否启用虚拟化 */
  enabled: boolean;
  /** 视口缓冲区大小（像素） */
  bufferSize: number;
  /** 最小渲染项目数 */
  minRenderItems: number;
  /** 最大渲染项目数 */
  maxRenderItems: number;
  /** 项目高度（用于列表虚拟化） */
  itemHeight?: number;
  /** 项目宽度（用于网格虚拟化） */
  itemWidth?: number;
  /** 是否启用动态高度 */
  dynamicHeight: boolean;
  /** 预渲染项目数 */
  preRenderCount: number;
}

/**
 * 虚拟化项目接口
 */
export interface VirtualizedItem {
  /** 项目ID */
  id: string;
  /** 项目索引 */
  index: number;
  /** 项目位置 */
  position: Vector2;
  /** 项目尺寸 */
  size: Vector2;
  /** 是否可见 */
  visible: boolean;
  /** 是否已渲染 */
  rendered: boolean;
  /** 关联的UI实体 */
  entity?: Entity;
  /** 项目数据 */
  data?: any;
}

/**
 * 视口信息接口
 */
export interface ViewportInfo {
  /** 视口位置 */
  position: Vector2;
  /** 视口尺寸 */
  size: Vector2;
  /** 滚动偏移 */
  scrollOffset: Vector2;
  /** 缩放比例 */
  scale: number;
}

/**
 * UI虚拟化渲染系统
 */
export class UIVirtualizationSystem extends System {
  private config: VirtualizationConfig;
  private viewport: ViewportInfo;
  private items: Map<string, VirtualizedItem> = new Map();
  private visibleItems: Set<string> = new Set();
  private renderedItems: Set<string> = new Set();
  private itemPool: Entity[] = [];
  private scrollContainer?: Entity;
  private totalContentSize: Vector2 = new Vector2();
  private lastUpdateTime: number = 0;
  private updateThrottle: number = 16; // 60fps

  constructor() {
    super();
    this.config = {
      enabled: true,
      bufferSize: 100,
      minRenderItems: 10,
      maxRenderItems: 100,
      dynamicHeight: false,
      preRenderCount: 5
    };
    
    this.viewport = {
      position: new Vector2(),
      size: new Vector2(800, 600),
      scrollOffset: new Vector2(),
      scale: 1.0
    };
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    super.initialize();
    this.setupEventListeners();
  }

  /**
   * 更新系统
   */
  public update(deltaTime: number): void {
    if (!this.config.enabled) {
      return;
    }

    const currentTime = performance.now();
    if (currentTime - this.lastUpdateTime < this.updateThrottle) {
      return;
    }

    this.updateVisibleItems();
    this.updateRenderedItems();
    
    this.lastUpdateTime = currentTime;
  }

  /**
   * 设置虚拟化配置
   */
  public setConfig(config: Partial<VirtualizationConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 设置视口信息
   */
  public setViewport(viewport: Partial<ViewportInfo>): void {
    this.viewport = { ...this.viewport, ...viewport };
  }

  /**
   * 设置滚动容器
   */
  public setScrollContainer(container: Entity): void {
    this.scrollContainer = container;
    this.setupScrollListeners();
  }

  /**
   * 添加虚拟化项目
   */
  public addItem(item: Omit<VirtualizedItem, 'visible' | 'rendered'>): void {
    const virtualizedItem: VirtualizedItem = {
      ...item,
      visible: false,
      rendered: false
    };
    
    this.items.set(item.id, virtualizedItem);
    this.updateTotalContentSize();
  }

  /**
   * 移除虚拟化项目
   */
  public removeItem(id: string): void {
    const item = this.items.get(id);
    if (item) {
      if (item.entity) {
        this.recycleEntity(item.entity);
      }
      this.items.delete(id);
      this.visibleItems.delete(id);
      this.renderedItems.delete(id);
      this.updateTotalContentSize();
    }
  }

  /**
   * 清空所有项目
   */
  public clear(): void {
    // 回收所有实体
    for (const item of this.items.values()) {
      if (item.entity) {
        this.recycleEntity(item.entity);
      }
    }
    
    this.items.clear();
    this.visibleItems.clear();
    this.renderedItems.clear();
    this.totalContentSize.set(0, 0);
  }

  /**
   * 获取可见项目
   */
  public getVisibleItems(): VirtualizedItem[] {
    return Array.from(this.visibleItems).map(id => this.items.get(id)!);
  }

  /**
   * 获取总内容尺寸
   */
  public getTotalContentSize(): Vector2 {
    return this.totalContentSize.clone();
  }

  /**
   * 滚动到指定项目
   */
  public scrollToItem(id: string, alignment: 'start' | 'center' | 'end' = 'start'): void {
    const item = this.items.get(id);
    if (!item) return;

    let targetY = item.position.y;
    
    switch (alignment) {
      case 'center':
        targetY -= this.viewport.size.y / 2 - item.size.y / 2;
        break;
      case 'end':
        targetY -= this.viewport.size.y - item.size.y;
        break;
    }

    this.viewport.scrollOffset.y = Math.max(0, Math.min(targetY, 
      this.totalContentSize.y - this.viewport.size.y));
    
    this.updateVisibleItems();
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听窗口大小变化
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', () => {
        this.updateViewportSize();
      });
    }
  }

  /**
   * 设置滚动监听器
   */
  private setupScrollListeners(): void {
    if (!this.scrollContainer) return;

    // 这里应该监听滚动容器的滚动事件
    // 具体实现取决于UI系统的事件机制
  }

  /**
   * 更新可见项目
   */
  private updateVisibleItems(): void {
    const viewportBounds = this.getViewportBounds();
    const newVisibleItems = new Set<string>();

    for (const [id, item] of this.items) {
      const itemBounds = new Box2(
        item.position,
        new Vector2(item.position.x + item.size.x, item.position.y + item.size.y)
      );

      const isVisible = viewportBounds.intersectsBox(itemBounds);
      item.visible = isVisible;

      if (isVisible) {
        newVisibleItems.add(id);
      }
    }

    this.visibleItems = newVisibleItems;
  }

  /**
   * 更新已渲染项目
   */
  private updateRenderedItems(): void {
    const itemsToRender = new Set<string>();
    const itemsToUnrender = new Set<string>();

    // 确定需要渲染的项目
    for (const id of this.visibleItems) {
      if (!this.renderedItems.has(id)) {
        itemsToRender.add(id);
      }
    }

    // 确定需要取消渲染的项目
    for (const id of this.renderedItems) {
      if (!this.visibleItems.has(id)) {
        itemsToUnrender.add(id);
      }
    }

    // 限制渲染项目数量
    if (this.renderedItems.size + itemsToRender.size > this.config.maxRenderItems) {
      const excess = this.renderedItems.size + itemsToRender.size - this.config.maxRenderItems;
      // 如果需要取消渲染的项目不够，从已渲染的可见项目中选择一些来取消渲染
      if (itemsToUnrender.size < excess) {
        const additionalToUnrender = excess - itemsToUnrender.size;
        const visibleRenderedItems = Array.from(this.renderedItems).filter(id =>
          this.visibleItems.has(id) && !itemsToUnrender.has(id)
        );
        const itemsToRemove = visibleRenderedItems.slice(0, additionalToUnrender);
        itemsToRemove.forEach(id => itemsToUnrender.add(id));
      }
    }

    // 取消渲染项目
    for (const id of itemsToUnrender) {
      this.unrenderItem(id);
    }

    // 渲染新项目
    for (const id of itemsToRender) {
      this.renderItem(id);
    }
  }

  /**
   * 渲染项目
   */
  private renderItem(id: string): void {
    const item = this.items.get(id);
    if (!item || item.rendered) return;

    // 从对象池获取或创建实体
    let entity = this.getEntityFromPool();
    if (!entity) {
      entity = this.createItemEntity(item);
    } else {
      this.updateItemEntity(entity, item);
    }

    item.entity = entity;
    item.rendered = true;
    this.renderedItems.add(id);
  }

  /**
   * 取消渲染项目
   */
  private unrenderItem(id: string): void {
    const item = this.items.get(id);
    if (!item || !item.rendered || !item.entity) return;

    this.recycleEntity(item.entity);
    item.entity = undefined;
    item.rendered = false;
    this.renderedItems.delete(id);
  }



  /**
   * 获取视口边界
   */
  private getViewportBounds(): Box2 {
    const min = new Vector2(
      this.viewport.scrollOffset.x - this.config.bufferSize,
      this.viewport.scrollOffset.y - this.config.bufferSize
    );
    const max = new Vector2(
      this.viewport.scrollOffset.x + this.viewport.size.x + this.config.bufferSize,
      this.viewport.scrollOffset.y + this.viewport.size.y + this.config.bufferSize
    );
    
    return new Box2(min, max);
  }

  /**
   * 更新总内容尺寸
   */
  private updateTotalContentSize(): void {
    let maxX = 0;
    let maxY = 0;

    for (const item of this.items.values()) {
      maxX = Math.max(maxX, item.position.x + item.size.x);
      maxY = Math.max(maxY, item.position.y + item.size.y);
    }

    this.totalContentSize.set(maxX, maxY);
  }

  /**
   * 更新视口尺寸
   */
  private updateViewportSize(): void {
    // 这里应该根据实际的渲染目标更新视口尺寸
    // 具体实现取决于渲染系统
  }

  /**
   * 从对象池获取实体
   */
  private getEntityFromPool(): Entity | null {
    return this.itemPool.pop() || null;
  }

  /**
   * 回收实体到对象池
   */
  private recycleEntity(entity: Entity): void {
    // 重置实体状态
    this.resetEntity(entity);
    this.itemPool.push(entity);
  }

  /**
   * 创建项目实体
   */
  private createItemEntity(_item: VirtualizedItem): Entity {
    // 这里应该根据项目数据创建相应的UI实体
    // 具体实现取决于UI组件系统
    const entity = new Entity();
    // 添加UI组件和设置属性...
    return entity;
  }

  /**
   * 更新项目实体
   */
  private updateItemEntity(entity: Entity, _item: VirtualizedItem): void {
    // 更新实体的位置、尺寸和其他属性
    const uiComponent = entity.getComponent<UIComponent>('UIComponent');
    if (uiComponent) {
      // 更新UI组件属性...
    }
  }

  /**
   * 重置实体状态
   */
  private resetEntity(entity: Entity): void {
    // 重置实体到初始状态，准备重用
    const uiComponent = entity.getComponent<UIComponent>('UIComponent');
    if (uiComponent) {
      // 重置UI组件属性...
    }
  }
}
