# AsyncNode 使用指南

## 概述

AsyncNode 是视觉脚本系统中用于处理异步操作的基类。它提供了完整的异步操作管理功能，包括状态管理、超时控制、重试机制、进度报告和取消操作等。

## 主要功能

### 1. 异步状态管理
- **IDLE**: 空闲状态
- **RUNNING**: 运行中
- **COMPLETED**: 已完成
- **CANCELED**: 已取消
- **ERROR**: 出错

### 2. 超时控制
- 支持设置操作超时时间
- 超时后自动触发错误处理

### 3. 重试机制
- 支持设置最大重试次数
- 可配置重试延迟时间
- 失败后自动重试

### 4. 进度报告
- 支持实时进度更新（0-100%）
- 提供进度回调机制

### 5. 取消操作
- 支持中途取消正在进行的异步操作
- 使用 AbortController 进行取消控制

## 配置选项

```typescript
interface AsyncNodeOptions extends FlowNodeOptions {
  /** 超时时间（毫秒） */
  timeout?: number;
  /** 最大重试次数 */
  maxRetries?: number;
  /** 重试延迟（毫秒） */
  retryDelay?: number;
  /** 是否支持取消 */
  cancelable?: boolean;
  /** 是否支持进度报告 */
  progressReporting?: boolean;
}
```

## 使用方法

### 1. 创建自定义异步节点

```typescript
export class MyAsyncNode extends AsyncNode {
  constructor(options: AsyncNodeOptions) {
    super({
      ...options,
      timeout: 30000,        // 30秒超时
      maxRetries: 3,         // 最多重试3次
      retryDelay: 1000,      // 重试延迟1秒
      cancelable: true,      // 支持取消
      progressReporting: true // 支持进度报告
    });
  }

  protected initializeSockets(): void {
    super.initializeSockets();
    
    // 添加自定义输入输出插槽
    this.addInput({
      name: 'inputData',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '输入数据'
    });

    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '处理结果'
    });
  }

  protected async executeAsync(inputs: Record<string, any>): Promise<any> {
    const inputData = inputs.inputData;
    
    // 执行异步操作
    for (let i = 0; i <= 100; i += 10) {
      // 检查是否被取消
      if (this.getAbortController()?.signal.aborted) {
        throw new Error('操作已取消');
      }
      
      // 更新进度
      this.updateProgress(i);
      
      // 模拟异步工作
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    return {
      result: `处理完成: ${inputData}`,
      timestamp: Date.now()
    };
  }
}
```

### 2. 处理网络请求

```typescript
export class HTTPRequestNode extends AsyncNode {
  protected async executeAsync(inputs: Record<string, any>): Promise<any> {
    const url = inputs.url;
    const method = inputs.method || 'GET';
    
    const response = await fetch(url, {
      method,
      signal: this.getAbortController()?.signal // 支持取消
    });
    
    const data = await response.json();
    
    return {
      response: data,
      statusCode: response.status,
      headers: Object.fromEntries(response.headers.entries())
    };
  }
}
```

### 3. 处理文件操作

```typescript
export class FileReadNode extends AsyncNode {
  protected async executeAsync(inputs: Record<string, any>): Promise<any> {
    const file = inputs.file as File;
    
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onprogress = (event) => {
        if (event.lengthComputable) {
          const progress = (event.loaded / event.total) * 100;
          this.updateProgress(progress);
        }
      };
      
      reader.onload = () => {
        resolve({
          content: reader.result,
          size: file.size
        });
      };
      
      reader.onerror = () => reject(new Error('文件读取失败'));
      
      // 支持取消
      this.getAbortController()?.signal.addEventListener('abort', () => {
        reader.abort();
      });
      
      reader.readAsText(file);
    });
  }
}
```

## 输出流程

AsyncNode 提供以下输出流程：

- **complete**: 操作成功完成时触发
- **error**: 操作失败时触发
- **canceled**: 操作被取消时触发（仅当 cancelable=true 时）

## 输出数据

AsyncNode 提供以下输出数据：

- **state**: 当前状态字符串
- **error**: 错误信息对象
- **progress**: 当前进度（0-100，仅当 progressReporting=true 时）

## 回调函数

```typescript
const asyncNode = new MyAsyncNode(options);

// 设置完成回调
asyncNode.onComplete(() => {
  console.log('操作完成');
});

// 设置错误回调
asyncNode.onError((error) => {
  console.error('操作失败:', error);
});

// 设置取消回调
asyncNode.onCancel(() => {
  console.log('操作被取消');
});

// 设置进度回调
asyncNode.onProgress((progress) => {
  console.log(`进度: ${progress}%`);
});
```

## 手动控制

```typescript
// 取消操作
asyncNode.cancel();

// 重置状态
asyncNode.reset();

// 获取状态信息
const state = asyncNode.getState();
const progress = asyncNode.getProgress();
const retries = asyncNode.getCurrentRetries();
const runningTime = asyncNode.getRunningTime();
```

## 最佳实践

1. **合理设置超时时间**: 根据操作的预期执行时间设置合适的超时值
2. **使用进度报告**: 对于长时间运行的操作，启用进度报告提升用户体验
3. **支持取消操作**: 对于可能需要中断的操作，启用取消功能
4. **错误处理**: 在 executeAsync 方法中妥善处理各种异常情况
5. **资源清理**: 在操作被取消时，确保正确清理资源

## 示例节点

系统提供了以下示例异步节点：

- **AsyncDelayNode**: 异步延迟节点
- **AsyncHTTPRequestNode**: 异步HTTP请求节点
- **AsyncFileReadNode**: 异步文件读取节点

这些示例展示了 AsyncNode 的各种使用方式，可以作为开发自定义异步节点的参考。
