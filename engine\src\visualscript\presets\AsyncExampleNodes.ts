/**
 * 异步节点示例
 * 展示如何使用AsyncNode基类创建具体的异步节点
 */
import { AsyncNode, AsyncNodeOptions } from '../nodes/AsyncNode';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';

/**
 * 延迟节点
 * 延迟指定时间后完成
 */
export class AsyncDelayNode extends AsyncNode {
  constructor(options: AsyncNodeOptions) {
    super({
      ...options,
      timeout: 0, // 不设置超时，因为延迟本身就是时间控制
      cancelable: true,
      progressReporting: true
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '异步延迟';
    }
    if (!this.metadata.description) {
      this.metadata.description = '延迟指定时间后完成';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加延迟时间输入
    this.addInput({
      name: 'duration',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '延迟时间（秒）',
      defaultValue: 1.0
    });
  }

  protected async executeAsync(inputs: Record<string, any>): Promise<void> {
    const duration = Math.max(0, inputs.duration || 1.0) * 1000; // 转换为毫秒
    const startTime = Date.now();

    return new Promise((resolve, reject) => {
      const updateProgress = () => {
        if (this.getAbortController()?.signal.aborted) {
          reject(new Error('操作已取消'));
          return;
        }

        const elapsed = Date.now() - startTime;
        const progress = Math.min(100, (elapsed / duration) * 100);
        this.updateProgress(progress);

        if (elapsed >= duration) {
          resolve();
        } else {
          setTimeout(updateProgress, 50); // 每50ms更新一次进度
        }
      };

      updateProgress();
    });
  }
}

/**
 * HTTP请求节点
 * 发送HTTP请求并返回响应
 */
export class AsyncHTTPRequestNode extends AsyncNode {
  constructor(options: AsyncNodeOptions) {
    super({
      ...options,
      timeout: 30000, // 默认30秒超时
      maxRetries: 3, // 默认重试3次
      retryDelay: 1000, // 重试延迟1秒
      cancelable: true,
      progressReporting: false
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '异步HTTP请求';
    }
    if (!this.metadata.description) {
      this.metadata.description = '发送HTTP请求并返回响应';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加输入
    this.addInput({
      name: 'url',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '请求URL',
      defaultValue: 'https://api.example.com'
    });

    this.addInput({
      name: 'method',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '请求方法',
      defaultValue: 'GET'
    });

    this.addInput({
      name: 'headers',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '请求头',
      defaultValue: {},
      optional: true
    });

    this.addInput({
      name: 'body',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '请求体',
      optional: true
    });

    // 添加输出
    this.addOutput({
      name: 'response',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '响应数据'
    });

    this.addOutput({
      name: 'statusCode',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '状态码'
    });

    this.addOutput({
      name: 'headers',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '响应头'
    });
  }

  protected async executeAsync(inputs: Record<string, any>): Promise<any> {
    const url = inputs.url;
    const method = inputs.method || 'GET';
    const headers = inputs.headers || {};
    const body = inputs.body;

    const fetchOptions: RequestInit = {
      method,
      headers,
      signal: this.getAbortController()?.signal
    };

    // 添加请求体
    if (body !== undefined) {
      fetchOptions.body = typeof body === 'string' ? body : JSON.stringify(body);
    }

    const response = await fetch(url, fetchOptions);

    // 获取响应数据
    const responseData = await response.text();
    let parsedData: any;
    try {
      parsedData = JSON.parse(responseData);
    } catch {
      parsedData = responseData;
    }

    return {
      response: parsedData,
      statusCode: response.status,
      headers: Object.fromEntries(response.headers.entries())
    };
  }
}

/**
 * 文件读取节点
 * 异步读取文件内容
 */
export class AsyncFileReadNode extends AsyncNode {
  constructor(options: AsyncNodeOptions) {
    super({
      ...options,
      timeout: 10000, // 默认10秒超时
      maxRetries: 2, // 默认重试2次
      retryDelay: 500, // 重试延迟500ms
      cancelable: true,
      progressReporting: true
    });

    // 设置节点元数据
    if (!this.metadata.name) {
      this.metadata.name = '异步文件读取';
    }
    if (!this.metadata.description) {
      this.metadata.description = '异步读取文件内容';
    }
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 添加输入
    this.addInput({
      name: 'file',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '文件对象（File）'
    });

    this.addInput({
      name: 'encoding',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '编码格式',
      defaultValue: 'utf-8',
      optional: true
    });

    // 添加输出
    this.addOutput({
      name: 'content',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '文件内容'
    });

    this.addOutput({
      name: 'size',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '文件大小'
    });
  }

  protected async executeAsync(inputs: Record<string, any>): Promise<any> {
    const file = inputs.file as File;
    const encoding = inputs.encoding || 'utf-8';

    if (!file || !(file instanceof File)) {
      throw new Error('无效的文件对象');
    }

    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      let loaded = 0;

      reader.onprogress = (event) => {
        if (event.lengthComputable) {
          loaded = event.loaded;
          const progress = (loaded / event.total) * 100;
          this.updateProgress(progress);
        }
      };

      reader.onload = () => {
        this.updateProgress(100);
        resolve({
          content: reader.result as string,
          size: file.size
        });
      };

      reader.onerror = () => {
        reject(new Error('文件读取失败'));
      };

      reader.onabort = () => {
        reject(new Error('文件读取被取消'));
      };

      // 监听取消信号
      if (this.getAbortController()) {
        this.getAbortController()!.signal.addEventListener('abort', () => {
          reader.abort();
        });
      }

      reader.readAsText(file, encoding);
    });
  }
}

/**
 * 注册异步示例节点
 * @param registry 节点注册表
 */
export function registerAsyncExampleNodes(registry: NodeRegistry): void {
  // 注册异步延迟节点
  registry.registerNodeType({
    type: 'async/delay',
    category: NodeCategory.FLOW,
    constructor: AsyncDelayNode,
    label: '异步延迟',
    description: '延迟指定时间后完成',
    icon: 'timer',
    color: '#FF9800',
    tags: ['async', 'delay', 'timer']
  });

  // 注册异步HTTP请求节点
  registry.registerNodeType({
    type: 'async/httpRequest',
    category: NodeCategory.NETWORK,
    constructor: AsyncHTTPRequestNode,
    label: '异步HTTP请求',
    description: '发送HTTP请求并返回响应',
    icon: 'http',
    color: '#2196F3',
    tags: ['async', 'http', 'request', 'network']
  });

  // 注册异步文件读取节点
  registry.registerNodeType({
    type: 'async/fileRead',
    category: NodeCategory.IO,
    constructor: AsyncFileReadNode,
    label: '异步文件读取',
    description: '异步读取文件内容',
    icon: 'file',
    color: '#4CAF50',
    tags: ['async', 'file', 'read', 'io']
  });
}
